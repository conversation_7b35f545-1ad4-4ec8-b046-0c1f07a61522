import re
import jieba
import onnxruntime
import numpy as np
from pydub import AudioSegment
from pypinyin import lazy_pinyin, Style
import soundfile as sf
import logging
from typing import Tuple, Optional

logger = logging.getLogger(__name__)

class F5TTSWrapper:
    def __init__(self, model_type="F5-TTS_v1"):
        self.model_type = model_type
        self.load_models()
        self.load_vocab()
        
    def load_vocab(self):
        vocab_path = "/root/researches/model_files/Swvid/vocab.txt"  # Update with your path
        with open(vocab_path, "r", encoding="utf-8") as f:
            self.vocab_char_map = {}
            for i, char in enumerate(f):
                self.vocab_char_map[char[:-1]] = i
        self.vocab_size = len(self.vocab_char_map)
    
    def load_models(self):
        # Session options
        session_opts = onnxruntime.SessionOptions()
        session_opts.inter_op_num_threads = 8
        session_opts.intra_op_num_threads = 8
        session_opts.enable_cpu_mem_arena = True
        session_opts.execution_mode = onnxruntime.ExecutionMode.ORT_SEQUENTIAL
        session_opts.graph_optimization_level = onnxruntime.GraphOptimizationLevel.ORT_ENABLE_ALL

        # Model paths - update these with your actual paths
        self.preprocess_model = onnxruntime.InferenceSession(
            "/root/researches/F5TTSapi/F5_ONNX/F5_Preprocess.onnx",
            sess_options=session_opts,
            providers=['CPUExecutionProvider']
        )
        
        self.transformer_model = onnxruntime.InferenceSession(
            "/root/researches/F5TTSapi/F5_ONNX/F5_Transformer.onnx",
            sess_options=session_opts,
            providers=['CPUExecutionProvider']
        )
        
        self.decode_model = onnxruntime.InferenceSession(
            "/root/researches/F5TTSapi/F5_ONNX/F5_Decode.onnx",
            sess_options=session_opts,
            providers=['CPUExecutionProvider']
        )

    def convert_char_to_pinyin(self, text_list, polyphone=True):
        if jieba.dt.initialized is False:
            jieba.default_logger.setLevel(50)  # CRITICAL
            jieba.initialize()

        final_text_list = []
        custom_trans = str.maketrans(
            {";": ",", "“": '"', "”": '"', "‘": "'", "’": "'"}
        )

        def is_chinese(c):
            return "\u3100" <= c <= "\u9fff"

        for text in text_list:
            char_list = []
            text = text.translate(custom_trans)
            for seg in jieba.cut(text):
                seg_byte_len = len(bytes(seg, "UTF-8"))
                if seg_byte_len == len(seg):
                    if char_list and seg_byte_len > 1 and char_list[-1] not in " :'\"":
                        char_list.append(" ")
                    char_list.extend(seg)
                elif polyphone and seg_byte_len == 3 * len(seg):
                    seg_ = lazy_pinyin(seg, style=Style.TONE3, tone_sandhi=True)
                    for i, c in enumerate(seg):
                        if is_chinese(c):
                            char_list.append(" ")
                        char_list.append(seg_[i])
                else:
                    for c in seg:
                        if ord(c) < 256:
                            char_list.extend(c)
                        elif is_chinese(c):
                            char_list.append(" ")
                            char_list.extend(lazy_pinyin(c, style=Style.TONE3, tone_sandhi=True))
                        else:
                            char_list.append(c)
            final_text_list.append(char_list)
        return final_text_list

    def list_str_to_idx(self, text, padding_value=-1):
        get_idx = self.vocab_char_map.get
        list_idx_tensors = [np.array([get_idx(c, 0) for c in t], dtype=np.int32) for t in text]
        max_len = max(len(t) for t in list_idx_tensors)
        text = np.stack([np.pad(t, (0, max_len - len(t)), constant_values=padding_value) 
                      for t in list_idx_tensors])
        return text

    def normalize_to_int16(self, audio):
        max_val = np.max(np.abs(audio))
        scaling_factor = 32767.0 / max_val if max_val > 0 else 1.0
        return (audio * float(scaling_factor)).astype(np.int16)

    def infer(
        self,
        ref_audio_orig: str,
        ref_text: Optional[str] = None,
        gen_text: str = "",
        model_type: str = "F5-TTS_v1",
        remove_silence: bool = False,
        seed: int = -1,
        cross_fade_duration: float = 0.15,
        nfe_step: int = 32,
        speed: float = 1.0,
        show_info=None
    ) -> Tuple[Tuple[int, np.ndarray], Optional[str], Optional[str], int]:
        """
        Enhanced inference method with parameters matching infer_gradio structure
        """
        if show_info:
            show_info(f"Starting synthesis with model: {model_type}")
            show_info(f"Parameters - seed: {seed}, nfe_step: {nfe_step}, speed: {speed}")

        # Load and process reference audio
        audio = np.array(AudioSegment.from_file(ref_audio_orig)
                         .set_channels(1)
                         .set_frame_rate(24000)  # F5-TTS sample rate
                         .get_array_of_samples(), dtype=np.float32)
        audio = self.normalize_to_int16(audio)
        audio_len = len(audio)
        audio = audio.reshape(1, 1, -1)

        # Process text
        zh_pause_punc = r"。，、；：？！"
        text_len = len(gen_text.encode('utf-8')) + 3 * len(re.findall(zh_pause_punc, gen_text))
        audio_len_frames = audio_len // 256 + 1  # hop_length=256
        max_duration = np.array([audio_len_frames + int(audio_len_frames / text_len * text_len / speed)], 
                              dtype=np.int64)
        
        pinyin_text = self.convert_char_to_pinyin([gen_text])
        text_ids = self.list_str_to_idx(pinyin_text)
        time_step_1 = np.array([0], dtype=np.int32)

        # Run preprocessing
        noise, rope_cos_q, rope_sin_q, rope_cos_k, rope_sin_k, cat_mel_text, cat_mel_text_drop, ref_signal_len = (
            self.preprocess_model.run(
                None,  # None will return all outputs
                {
                    'audio': audio,
                    'text_ids': text_ids,
                    'max_duration': max_duration
                }
            )
        )

    

        # Run transformer
        for i in range(0, nfe_step - 1, 1):  # fuse_nfe=1
            # The model has two outputs: 'denoised' and 'time_step'
            outputs = self.transformer_model.run(
                None,
                {
                    'noise': noise,
                    'rope_cos_q': rope_cos_q,
                    'rope_sin_q': rope_sin_q,
                    'rope_cos_k': rope_cos_k,
                    'rope_sin_k': rope_sin_k,
                    'cat_mel_text': cat_mel_text,
                    'cat_mel_text_drop': cat_mel_text_drop,
                    # Provide the single, correctly named time step input
                    'time_step.1': time_step_1
                }
            )
            # Unpack outputs (assume output[0] = noise, output[1] = time_step, output[2] = time_step_1)
    
            noise = outputs[0]       # This corresponds to the 'denoised' output
            time_step_1 = outputs[1] # This corresponds to the 'time_step' output for the next iteration

        # Run decode
        generated_signal = self.decode_model.run(
            None,
            {
                'noise': noise,
                'ref_signal_len': ref_signal_len,
            }
        )[0]

        return (24000, generated_signal.reshape(-1)), None, ref_text, seed if seed != -1 else 9527
