import re
import jieba
import onnxruntime
import numpy as np
from pydub import AudioSegment
from pypinyin import lazy_pinyin, Style
import soundfile as sf
import logging
from typing import Tuple, Optional
import os

logger = logging.getLogger(__name__)

class F5TTSWrapper:
    def __init__(self, model_type="F5-TTS_v1"):
        self.model_type = model_type
        self.max_vocab_size = 4096  # ONNX model vocab size limit
        self.load_models()
        self.load_vocab()

    def load_vocab(self):
        # Try multiple vocab paths
        vocab_paths = [
            "/root/researches/model_files/Swvid/vocab.txt",
            "/root/researches/F5TTSapi/.venv/lib/python3.10/dist-packages/f5_tts/infer/examples/vocab.txt",
            "/root/researches/f5ttslocal/.venv/lib/python3.10/site-packages/f5_tts/infer/examples/vocab.txt"
        ]

        vocab_path = None
        for path in vocab_paths:
            if os.path.exists(path):
                vocab_path = path
                break

        if not vocab_path:
            raise FileNotFoundError("Could not find vocab.txt file in any of the expected locations")

        logger.info(f"Loading vocab from: {vocab_path}")

        with open(vocab_path, "r", encoding="utf-8") as f:
            self.vocab_char_map = {}
            for i, char in enumerate(f):
                char = char.strip()
                if char:  # Skip empty lines
                    self.vocab_char_map[char] = i

        self.vocab_size = len(self.vocab_char_map)
        logger.info(f"Loaded vocab with {self.vocab_size} characters")

        # Add safety check for vocab size
        if self.vocab_size >= self.max_vocab_size:
            logger.warning(f"Vocab size {self.vocab_size} exceeds ONNX model limit {self.max_vocab_size}")

    def load_models(self):
        # Optimized session options for better performance
        session_opts = onnxruntime.SessionOptions()
        session_opts.inter_op_num_threads = min(8, os.cpu_count() or 4)
        session_opts.intra_op_num_threads = min(8, os.cpu_count() or 4)
        session_opts.enable_cpu_mem_arena = True
        session_opts.enable_mem_pattern = True
        session_opts.enable_mem_reuse = True
        session_opts.execution_mode = onnxruntime.ExecutionMode.ORT_SEQUENTIAL
        session_opts.graph_optimization_level = onnxruntime.GraphOptimizationLevel.ORT_ENABLE_ALL

        # Try to use available providers in order of preference
        available_providers = onnxruntime.get_available_providers()
        providers = []

        if 'CUDAExecutionProvider' in available_providers:
            providers.append(('CUDAExecutionProvider', {
                'device_id': 0,
                'arena_extend_strategy': 'kNextPowerOfTwo',
                'gpu_mem_limit': 2 * 1024 * 1024 * 1024,  # 2GB limit
                'cudnn_conv_algo_search': 'EXHAUSTIVE',
                'do_copy_in_default_stream': True,
            }))
            logger.info("Using CUDA acceleration")

        providers.append('CPUExecutionProvider')

        # Model paths
        model_dir = "/root/researches/F5TTSapi/F5_ONNX"

        self.preprocess_model = onnxruntime.InferenceSession(
            f"{model_dir}/F5_Preprocess.onnx",
            sess_options=session_opts,
            providers=providers
        )

        self.transformer_model = onnxruntime.InferenceSession(
            f"{model_dir}/F5_Transformer.onnx",
            sess_options=session_opts,
            providers=providers
        )

        self.decode_model = onnxruntime.InferenceSession(
            f"{model_dir}/F5_Decode.onnx",
            sess_options=session_opts,
            providers=providers
        )

        logger.info(f"Models loaded with providers: {self.preprocess_model.get_providers()}")

    def convert_char_to_pinyin(self, text_list, polyphone=True):
        if jieba.dt.initialized is False:
            jieba.default_logger.setLevel(50)  # CRITICAL
            jieba.initialize()

        final_text_list = []
        custom_trans = str.maketrans(
            {";": ",", "“": '"', "”": '"', "‘": "'", "’": "'"}
        )

        def is_chinese(c):
            return "\u3100" <= c <= "\u9fff"

        for text in text_list:
            char_list = []
            text = text.translate(custom_trans)
            for seg in jieba.cut(text):
                seg_byte_len = len(bytes(seg, "UTF-8"))
                if seg_byte_len == len(seg):
                    if char_list and seg_byte_len > 1 and char_list[-1] not in " :'\"":
                        char_list.append(" ")
                    char_list.extend(seg)
                elif polyphone and seg_byte_len == 3 * len(seg):
                    seg_ = lazy_pinyin(seg, style=Style.TONE3, tone_sandhi=True)
                    for i, c in enumerate(seg):
                        if is_chinese(c):
                            char_list.append(" ")
                        char_list.append(seg_[i])
                else:
                    for c in seg:
                        if ord(c) < 256:
                            char_list.extend(c)
                        elif is_chinese(c):
                            char_list.append(" ")
                            char_list.extend(lazy_pinyin(c, style=Style.TONE3, tone_sandhi=True))
                        else:
                            char_list.append(c)
            final_text_list.append(char_list)
        return final_text_list

    def list_str_to_idx(self, text, padding_value=-1):
        get_idx = self.vocab_char_map.get
        list_idx_tensors = []

        for t in text:
            indices = []
            for c in t:
                idx = get_idx(c, 0)  # Default to 0 if character not found
                # Ensure index is within ONNX model bounds
                if idx >= self.max_vocab_size - 1:  # Reserve space for +1 offset
                    logger.warning(f"Character '{c}' index {idx} exceeds vocab limit, using 0")
                    idx = 0
                indices.append(idx)
            list_idx_tensors.append(np.array(indices, dtype=np.int32))

        if not list_idx_tensors:
            return np.array([[]], dtype=np.int32)

        max_len = max(len(t) for t in list_idx_tensors)
        text = np.stack([np.pad(t, (0, max_len - len(t)), constant_values=padding_value)
                      for t in list_idx_tensors])
        return text

    def normalize_to_int16(self, audio):
        max_val = np.max(np.abs(audio))
        scaling_factor = 32767.0 / max_val if max_val > 0 else 1.0
        return (audio * float(scaling_factor)).astype(np.int16)

    def infer(
        self,
        ref_audio_orig: str,
        ref_text: Optional[str] = None,
        gen_text: str = "",
        model_type: str = "F5-TTS_v1",
        remove_silence: bool = False,
        seed: int = -1,
        cross_fade_duration: float = 0.15,
        nfe_step: int = 32,
        speed: float = 1.0,
        show_info=None
    ) -> Tuple[Tuple[int, np.ndarray], Optional[str], Optional[str], int]:
        """
        Enhanced inference method with parameters matching infer_gradio structure
        """
        try:
            if show_info:
                show_info(f"Starting synthesis with model: {model_type}")
                show_info(f"Parameters - seed: {seed}, nfe_step: {nfe_step}, speed: {speed}")

            # Validate inputs
            if not gen_text.strip():
                raise ValueError("Generation text cannot be empty")

            if not os.path.exists(ref_audio_orig):
                raise FileNotFoundError(f"Reference audio file not found: {ref_audio_orig}")

            # Load and process reference audio with better error handling
            try:
                audio_segment = AudioSegment.from_file(ref_audio_orig)
                audio = np.array(audio_segment
                               .set_channels(1)
                               .set_frame_rate(24000)  # F5-TTS sample rate
                               .get_array_of_samples(), dtype=np.float32)
            except Exception as e:
                raise ValueError(f"Failed to load audio file: {str(e)}")

            if len(audio) == 0:
                raise ValueError("Audio file is empty or corrupted")

            audio = self.normalize_to_int16(audio)
            audio_len = len(audio)
            audio = audio.reshape(1, 1, -1)

            # Process text with better validation
            if show_info:
                show_info(f"Processing text: {gen_text[:50]}...")

            zh_pause_punc = r"。，、；：？！"
            text_len = len(gen_text.encode('utf-8')) + 3 * len(re.findall(zh_pause_punc, gen_text))

            if text_len == 0:
                raise ValueError("Text length calculation resulted in zero")

            audio_len_frames = audio_len // 256 + 1  # hop_length=256
            duration_multiplier = max(0.1, min(10.0, speed))  # Clamp speed to reasonable range
            max_duration = np.array([audio_len_frames + int(audio_len_frames / text_len * text_len / duration_multiplier)],
                                  dtype=np.int64)

            # Convert text to pinyin and get indices
            pinyin_text = self.convert_char_to_pinyin([gen_text])
            if show_info:
                show_info(f"Converted to pinyin, length: {len(pinyin_text[0]) if pinyin_text else 0}")

            text_ids = self.list_str_to_idx(pinyin_text)

            # Validate text_ids
            if text_ids.size == 0:
                raise ValueError("Text processing resulted in empty indices")

            # Check for out-of-bounds indices
            max_idx = np.max(text_ids[text_ids >= 0])  # Ignore padding values
            if max_idx >= self.max_vocab_size - 1:
                logger.warning(f"Max text index {max_idx} may cause issues with vocab size {self.vocab_size}")

            time_step_1 = np.array([0], dtype=np.int32)

            # Run preprocessing
            if show_info:
                show_info("Running preprocessing...")

            noise, rope_cos_q, rope_sin_q, rope_cos_k, rope_sin_k, cat_mel_text, cat_mel_text_drop, ref_signal_len = (
                self.preprocess_model.run(
                    None,  # None will return all outputs
                    {
                        'audio': audio,
                        'text_ids': text_ids,
                        'max_duration': max_duration
                    }
                )
            )

            # Run transformer with progress tracking
            if show_info:
                show_info(f"Running transformer for {nfe_step} steps...")

            for i in range(0, nfe_step - 1, 1):  # fuse_nfe=1
                if show_info and i % 8 == 0:  # Progress every 8 steps
                    show_info(f"Transformer step {i+1}/{nfe_step-1}")

                # The model has two outputs: 'denoised' and 'time_step'
                outputs = self.transformer_model.run(
                    None,
                    {
                        'noise': noise,
                        'rope_cos_q': rope_cos_q,
                        'rope_sin_q': rope_sin_q,
                        'rope_cos_k': rope_cos_k,
                        'rope_sin_k': rope_sin_k,
                        'cat_mel_text': cat_mel_text,
                        'cat_mel_text_drop': cat_mel_text_drop,
                        # Provide the single, correctly named time step input
                        'time_step.1': time_step_1
                    }
                )
                # Unpack outputs (assume output[0] = noise, output[1] = time_step, output[2] = time_step_1)

                noise = outputs[0]       # This corresponds to the 'denoised' output
                time_step_1 = outputs[1] # This corresponds to the 'time_step' output for the next iteration

            # Run decode
            if show_info:
                show_info("Running decoder...")

            generated_signal = self.decode_model.run(
                None,
                {
                    'denoised': noise,  # The final noise is the denoised signal
                    'ref_signal_len': ref_signal_len,
                }
            )[0]

            if show_info:
                show_info(f"Generated audio with shape: {generated_signal.shape}")

            return (24000, generated_signal.reshape(-1)), None, ref_text, seed if seed != -1 else 9527

        except Exception as e:
            logger.error(f"Inference failed: {str(e)}")
            raise
