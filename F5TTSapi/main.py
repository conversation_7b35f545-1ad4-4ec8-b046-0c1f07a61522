import logging
from fastapi import <PERSON><PERSON><PERSON>, UploadFile, Form, Request, HTTPException
from fastapi.responses import FileResponse
import tempfile
import soundfile as sf
from typing import Optional
import shutil
import os
from F5TTSonnx import F5TTSWrapper

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

app = FastAPI(title="F5-TTS ONNX API")

# Initialize TTS wrapper
tts = F5TTSWrapper()

@app.middleware("http")
async def log_requests(request: Request, call_next):
    logger.info("Incoming request: %s %s", request.method, request.url)
    response = await call_next(request)
    logger.info("Completed request with status code: %d", response.status_code)
    return response

@app.post("/synthesize/")
async def synthesize_speech(
    ref_audio: UploadFile,
    ref_text: Optional[str] = Form(None),
    gen_text: str = Form(...),
    model_type: Optional[str] = Form("F5-TTS_v1"),
    remove_silence: Optional[bool] = Form(False),
    seed: Optional[int] = Form(-1),
    cross_fade_duration: Optional[float] = Form(0.15),
    nfe_step: Optional[int] = Form(32),
    speed: Optional[float] = Form(1.0),
):
    """
    Synthesize speech using F5-TTS ONNX models
    
    Args:
        ref_audio: Reference audio file (WAV format)
        ref_text: Reference text (optional)
        gen_text: Text to generate speech for
        model_type: Model type (only F5-TTS_v1 supported in ONNX)
        remove_silence: Whether to remove silence (not implemented in ONNX)
        seed: Random seed (-1 for default)
        cross_fade_duration: Cross-fade duration (not implemented in ONNX)
        nfe_step: Number of denoising steps (default 32)
        speed: Speech speed (1.0 = normal)
    """
    try:
        # Save uploaded audio to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_audio:
            shutil.copyfileobj(ref_audio.file, tmp_audio)
            tmp_audio_path = tmp_audio.name

        # Run inference
        (sample_rate, audio_data), _, _, _ = tts.infer(
            ref_audio_orig=tmp_audio_path,
            ref_text=ref_text,
            gen_text=gen_text,
            model_type=model_type,
            nfe_step=nfe_step,
            speed=speed,
            show_info=logger.info
        )

        # Save output
        out_path = tempfile.NamedTemporaryFile(delete=False, suffix=".wav").name
        sf.write(out_path, audio_data, sample_rate)

        # Clean up
        os.unlink(tmp_audio_path)
        
        return FileResponse(out_path, media_type="audio/wav", filename="output.wav")

    except Exception as e:
        logger.error(f"Synthesis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/")
def root():
    return {
        "message": "F5-TTS ONNX API",
        "endpoints": {
            "/synthesize/": "POST with ref_audio and gen_text"
        }
    }