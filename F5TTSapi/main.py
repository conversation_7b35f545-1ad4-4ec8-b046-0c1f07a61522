import logging
from fastapi import <PERSON><PERSON><PERSON>, UploadFile, Form, Request, HTTPException
from fastapi.responses import FileResponse
import tempfile
import soundfile as sf
from typing import Optional
import shutil
import os
import time
from F5TTSonnx_optimized import F5TTSWrapperOptimized

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

app = FastAPI(title="F5-TTS ONNX API - Optimized")

# Initialize optimized TTS wrapper
try:
    tts = F5TTSWrapperOptimized()
    logger.info("F5-TTS ONNX wrapper initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize F5-TTS wrapper: {str(e)}")
    tts = None

@app.middleware("http")
async def log_requests(request: Request, call_next):
    logger.info("Incoming request: %s %s", request.method, request.url)
    response = await call_next(request)
    logger.info("Completed request with status code: %d", response.status_code)
    return response

@app.post("/synthesize/")
async def synthesize_speech(
    ref_audio: UploadFile,
    ref_text: Optional[str] = Form(None),
    gen_text: str = Form(...),
    model_type: Optional[str] = Form("F5-TTS_v1"),
    remove_silence: Optional[bool] = Form(False),
    seed: Optional[int] = Form(-1),
    cross_fade_duration: Optional[float] = Form(0.15),
    nfe_step: Optional[int] = Form(32),
    speed: Optional[float] = Form(1.0),
):
    """
    Synthesize speech using F5-TTS ONNX models (Optimized)

    Args:
        ref_audio: Reference audio file (WAV format)
        ref_text: Reference text (optional)
        gen_text: Text to generate speech for
        model_type: Model type (only F5-TTS_v1 supported in ONNX)
        remove_silence: Whether to remove silence (not implemented in ONNX)
        seed: Random seed (-1 for default)
        cross_fade_duration: Cross-fade duration (not implemented in ONNX)
        nfe_step: Number of denoising steps (default 32, min 8, max 64)
        speed: Speech speed (0.5-2.0, 1.0 = normal)
    """
    start_time = time.time()
    tmp_audio_path = None
    out_path = None

    try:
        # Check if TTS wrapper is available
        if tts is None:
            raise HTTPException(status_code=503, detail="TTS service is not available")

        # Validate inputs
        if not gen_text.strip():
            raise HTTPException(status_code=400, detail="Generation text cannot be empty")

        # Validate and clamp parameters
        nfe_step = max(8, min(64, nfe_step))  # Clamp between 8 and 64
        speed = max(0.5, min(2.0, speed))    # Clamp between 0.5 and 2.0

        # Check file type
        if not ref_audio.filename.lower().endswith(('.wav', '.mp3', '.flac', '.m4a')):
            raise HTTPException(status_code=400, detail="Unsupported audio format. Use WAV, MP3, FLAC, or M4A")

        logger.info(f"Processing request: text_len={len(gen_text)}, nfe_step={nfe_step}, speed={speed}")

        # Save uploaded audio to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_audio:
            shutil.copyfileobj(ref_audio.file, tmp_audio)
            tmp_audio_path = tmp_audio.name

        # Run optimized inference
        (sample_rate, audio_data), _, _, _ = tts.infer(
            ref_audio_orig=tmp_audio_path,
            ref_text=ref_text,
            gen_text=gen_text,
            model_type=model_type,
            nfe_step=nfe_step,
            speed=speed,
            show_info=logger.info
        )

        # Save output
        out_path = tempfile.NamedTemporaryFile(delete=False, suffix=".wav").name
        sf.write(out_path, audio_data, sample_rate)

        # Performance logging
        processing_time = time.time() - start_time
        audio_duration = len(audio_data) / sample_rate
        rtf = processing_time / audio_duration if audio_duration > 0 else 0
        logger.info(f"Synthesis completed: {processing_time:.2f}s processing, {audio_duration:.2f}s audio, RTF: {rtf:.2f}")

        return FileResponse(
            out_path,
            media_type="audio/wav",
            filename="output.wav",
            headers={
                "X-Processing-Time": str(processing_time),
                "X-Audio-Duration": str(audio_duration),
                "X-RTF": str(rtf)
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Synthesis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Synthesis failed: {str(e)}")
    finally:
        # Clean up temporary files
        if tmp_audio_path and os.path.exists(tmp_audio_path):
            try:
                os.unlink(tmp_audio_path)
            except:
                pass

@app.get("/health")
def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy" if tts is not None else "unhealthy",
        "service": "F5-TTS ONNX API - Optimized",
        "vocab_size": tts.vocab_size if tts else None,
        "providers": tts.preprocess_model.get_providers() if tts else None
    }

@app.get("/")
def root():
    return {
        "message": "F5-TTS ONNX API - Optimized",
        "status": "ready" if tts is not None else "service unavailable",
        "endpoints": {
            "/synthesize/": "POST with ref_audio and gen_text",
            "/health": "GET health check"
        },
        "optimizations": [
            "Vocab size validation",
            "Memory management",
            "CUDA acceleration (if available)",
            "Cached text processing",
            "Better error handling"
        ]
    }