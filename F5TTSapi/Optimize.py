import onnxruntime

# Make sure to use the correct path to your model
model_path = "/root/researches/F5TTSapi/F5_ONNX/F5_Transformer.onnx"

try:
    session = onnxruntime.InferenceSession(model_path)
    
    input_names = [inp.name for inp in session.get_inputs()]
    print("Model Input Names:", input_names)
    
    output_names = [out.name for out in session.get_outputs()]
    print("Model Output Names:", output_names)

except Exception as e:
    print(f"Failed to load or inspect the ONNX model: {e}")

