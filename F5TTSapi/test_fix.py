#!/usr/bin/env python3
"""
Test script to verify the F5-TTS ONNX fixes
"""

import logging
import sys
import os
import tempfile
import numpy as np
from pydub import AudioSegment
from F5TTSonnx_optimized import F5TTSWrapperOptimized

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

def create_test_audio(duration_seconds=3, sample_rate=24000):
    """Create a simple test audio file"""
    # Generate a simple sine wave
    t = np.linspace(0, duration_seconds, int(sample_rate * duration_seconds))
    frequency = 440  # A4 note
    audio = np.sin(2 * np.pi * frequency * t) * 0.3
    
    # Convert to int16
    audio_int16 = (audio * 32767).astype(np.int16)
    
    # Save to temporary file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
    
    # Create AudioSegment and export
    audio_segment = AudioSegment(
        audio_int16.tobytes(),
        frame_rate=sample_rate,
        sample_width=2,  # 16-bit
        channels=1
    )
    audio_segment.export(temp_file.name, format="wav")
    
    return temp_file.name

def test_vocab_loading():
    """Test vocab loading and size validation"""
    logger.info("Testing vocab loading...")
    try:
        tts = F5TTSWrapperOptimized()
        logger.info(f"✓ Vocab loaded successfully: {tts.vocab_size} characters")
        logger.info(f"✓ Max vocab size limit: {tts.max_vocab_size}")
        return True
    except Exception as e:
        logger.error(f"✗ Vocab loading failed: {str(e)}")
        return False

def test_model_loading():
    """Test ONNX model loading"""
    logger.info("Testing model loading...")
    try:
        tts = F5TTSWrapperOptimized()
        providers = tts.preprocess_model.get_providers()
        logger.info(f"✓ Models loaded successfully with providers: {providers}")
        return True
    except Exception as e:
        logger.error(f"✗ Model loading failed: {str(e)}")
        return False

def test_text_processing():
    """Test text processing with various inputs"""
    logger.info("Testing text processing...")
    try:
        tts = F5TTSWrapperOptimized()
        
        # Test cases
        test_texts = [
            "Hello world",
            "你好世界",
            "This is a test with mixed 中文 and English.",
            "Special characters: !@#$%^&*()",
            "Numbers: 123456789"
        ]
        
        for text in test_texts:
            try:
                pinyin_text = tts.convert_char_to_pinyin([text])
                text_ids = tts.list_str_to_idx(pinyin_text)
                
                # Check for out-of-bounds indices
                max_idx = np.max(text_ids[text_ids >= 0])
                if max_idx >= tts.max_vocab_size - 1:
                    logger.warning(f"⚠ Text '{text[:20]}...' has high indices: {max_idx}")
                else:
                    logger.info(f"✓ Text '{text[:20]}...' processed successfully, max_idx: {max_idx}")
                    
            except Exception as e:
                logger.error(f"✗ Failed to process text '{text[:20]}...': {str(e)}")
                return False
                
        return True
    except Exception as e:
        logger.error(f"✗ Text processing test failed: {str(e)}")
        return False

def test_inference():
    """Test full inference pipeline"""
    logger.info("Testing inference pipeline...")
    
    # Create test audio
    test_audio_path = None
    try:
        test_audio_path = create_test_audio()
        logger.info(f"✓ Created test audio: {test_audio_path}")
        
        # Initialize TTS
        tts = F5TTSWrapperOptimized()
        
        # Test inference
        test_text = "This is a test synthesis."
        logger.info(f"Testing synthesis with text: '{test_text}'")
        
        result = tts.infer(
            ref_audio_orig=test_audio_path,
            ref_text="Test reference text",
            gen_text=test_text,
            nfe_step=8,  # Use fewer steps for faster testing
            speed=1.0,
            show_info=logger.info
        )
        
        sample_rate, audio_data = result[0]
        logger.info(f"✓ Inference successful: {len(audio_data)} samples at {sample_rate}Hz")
        logger.info(f"✓ Audio duration: {len(audio_data) / sample_rate:.2f} seconds")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Inference test failed: {str(e)}")
        return False
    finally:
        # Clean up
        if test_audio_path and os.path.exists(test_audio_path):
            os.unlink(test_audio_path)

def main():
    """Run all tests"""
    logger.info("Starting F5-TTS ONNX fix verification tests...")
    
    tests = [
        ("Vocab Loading", test_vocab_loading),
        ("Model Loading", test_model_loading),
        ("Text Processing", test_text_processing),
        ("Inference Pipeline", test_inference)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running test: {test_name}")
        logger.info(f"{'='*50}")
        
        if test_func():
            passed += 1
            logger.info(f"✓ {test_name} PASSED")
        else:
            logger.error(f"✗ {test_name} FAILED")
    
    logger.info(f"\n{'='*50}")
    logger.info(f"Test Results: {passed}/{total} tests passed")
    logger.info(f"{'='*50}")
    
    if passed == total:
        logger.info("🎉 All tests passed! The fixes are working correctly.")
        return 0
    else:
        logger.error("❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
